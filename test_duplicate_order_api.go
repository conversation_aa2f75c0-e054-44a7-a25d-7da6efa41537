package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
)

// Test script to verify the duplicate order name API
func main() {
	baseURL := os.Getenv("API_BASE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080/api/v1"
	}

	// Test 1: Check for a name that doesn't exist
	testCheckDuplicateName(baseURL, "unique-order-name-123", "", false)

	// Test 2: Check for a name that exists (you'll need to create an order first)
	// testCheckDuplicateName(baseURL, "existing-order-name", "", true)

	// Test 3: Check with exclude_id parameter
	// testCheckDuplicateName(baseURL, "existing-order-name", "1", false)
}

func testCheckDuplicateName(baseURL, name, excludeID string, expectedDuplicate bool) {
	url := fmt.Sprintf("%s/orders/check-duplicate-name?name=%s", baseURL, name)
	if excludeID != "" {
		url += fmt.Sprintf("&exclude_id=%s", excludeID)
	}

	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("Error making request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		fmt.Printf("Error decoding response: %v\n", err)
		return
	}

	fmt.Printf("Test: name=%s, exclude_id=%s\n", name, excludeID)
	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", formatJSON(result))
	
	if data, ok := result["data"].(map[string]interface{}); ok {
		if isDuplicate, ok := data["is_duplicate"].(bool); ok {
			if isDuplicate == expectedDuplicate {
				fmt.Printf("✅ Test passed: expected %v, got %v\n", expectedDuplicate, isDuplicate)
			} else {
				fmt.Printf("❌ Test failed: expected %v, got %v\n", expectedDuplicate, isDuplicate)
			}
		}
	}
	fmt.Println("---")
}

func formatJSON(v interface{}) string {
	b, _ := json.MarshalIndent(v, "", "  ")
	return string(b)
}
