# Order Duplicate Name Check API

## Overview
This API endpoint allows you to check if an order name already exists in the system before creating or updating an order. This helps prevent duplicate order names and provides better user experience by validating names in real-time.

## Endpoint
```
GET /api/v1/orders/check-duplicate-name
```

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `name` | string | Yes | The order name to check for duplicates |
| `exclude_id` | uint64 | No | Order ID to exclude from the check (useful for updates) |

## Response Format

### Success Response (200 OK)
```json
{
  "status": "success",
  "message": "Order name is available", // or "Order name already exists"
  "data": {
    "name": "test-order-name",
    "is_duplicate": false
  }
}
```

### Error Response (400 Bad Request)
```json
{
  "status": "error",
  "message": "Name parameter is required",
  "data": null
}
```

### Error Response (500 Internal Server Error)
```json
{
  "status": "error",
  "message": "Internal server error message",
  "data": null
}
```

## Usage Examples

### 1. Check if a new order name is available
```bash
curl -X GET "http://localhost:8080/api/v1/orders/check-duplicate-name?name=my-new-order"
```

**Response:**
```json
{
  "status": "success",
  "message": "Order name is available",
  "data": {
    "name": "my-new-order",
    "is_duplicate": false
  }
}
```

### 2. Check if an existing order name is duplicate
```bash
curl -X GET "http://localhost:8080/api/v1/orders/check-duplicate-name?name=existing-order"
```

**Response:**
```json
{
  "status": "success",
  "message": "Order name already exists",
  "data": {
    "name": "existing-order",
    "is_duplicate": true
  }
}
```

### 3. Check for duplicate when updating an order (exclude current order)
```bash
curl -X GET "http://localhost:8080/api/v1/orders/check-duplicate-name?name=existing-order&exclude_id=123"
```

**Response:**
```json
{
  "status": "success",
  "message": "Order name is available",
  "data": {
    "name": "existing-order",
    "is_duplicate": false
  }
}
```

## Integration with Order Creation/Update

The duplicate check is automatically integrated into the order creation and update processes:

### Order Creation
- When creating a new order, the system automatically checks for duplicate names
- If a duplicate is found, the creation fails with error: "order with this name already exists"

### Order Update
- When updating an order, the system checks for duplicates excluding the current order
- If a duplicate is found, the update fails with error: "order with this name already exists"

## Frontend Integration Example

### JavaScript/TypeScript
```javascript
async function checkOrderNameDuplicate(name, excludeId = null) {
  const params = new URLSearchParams({ name });
  if (excludeId) {
    params.append('exclude_id', excludeId);
  }
  
  const response = await fetch(`/api/v1/orders/check-duplicate-name?${params}`);
  const result = await response.json();
  
  return result.data.is_duplicate;
}

// Usage in form validation
const isDuplicate = await checkOrderNameDuplicate('my-order-name');
if (isDuplicate) {
  showError('Order name already exists. Please choose a different name.');
}
```

### React Hook Example
```javascript
import { useState, useEffect } from 'react';

function useOrderNameValidation(name, excludeId = null) {
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!name) return;

    const checkDuplicate = async () => {
      setIsLoading(true);
      try {
        const duplicate = await checkOrderNameDuplicate(name, excludeId);
        setIsDuplicate(duplicate);
      } catch (error) {
        console.error('Error checking duplicate:', error);
      } finally {
        setIsLoading(false);
      }
    };

    const timeoutId = setTimeout(checkDuplicate, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [name, excludeId]);

  return { isDuplicate, isLoading };
}
```

## Error Handling

The API handles the following error scenarios:

1. **Missing name parameter**: Returns 400 Bad Request
2. **Invalid exclude_id parameter**: Ignores the parameter and continues
3. **Database connection issues**: Returns 500 Internal Server Error
4. **Service layer errors**: Returns 500 Internal Server Error

## Performance Considerations

- The API performs a simple database query by name
- Response time is typically under 100ms
- Consider implementing caching for frequently checked names if needed
- The endpoint is lightweight and suitable for real-time validation

## Security Notes

- No authentication is required for this endpoint as it only checks name availability
- The endpoint does not expose sensitive order information
- Rate limiting should be considered for production environments
