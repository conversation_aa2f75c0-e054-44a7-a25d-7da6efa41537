package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateServiceRequest struct {
	Name         string `json:"name" validate:"required,min=2,max=50"`
	Port         string `json:"port" validate:"required"`
	TargetPort   string `json:"target_port" validate:"required"`
	Type         string `json:"type" validate:"required,oneof=ClusterIP NodePort LoadBalancer"`
	ClusterIP    string `json:"cluster_ip,omitempty"`
	ExternalIP   string `json:"external_ip,omitempty"`
	IsActive     *bool  `json:"is_active,omitempty"`
	NamespaceID  uint64 `json:"namespace_id" validate:"required"`
	DeploymentID uint64 `json:"deployment_id" validate:"required"`
}

type UpdateServiceRequest struct {
	Name         string `json:"name" validate:"required,min=2,max=50"`
	Port         string `json:"port" validate:"required"`
	TargetPort   string `json:"target_port" validate:"required"`
	Type         string `json:"type" validate:"required,oneof=ClusterIP NodePort LoadBalancer"`
	ClusterIP    string `json:"cluster_ip,omitempty"`
	ExternalIP   string `json:"external_ip,omitempty"`
	IsActive     *bool  `json:"is_active,omitempty"`
	NamespaceID  uint64 `json:"namespace_id" validate:"required"`
	DeploymentID uint64 `json:"deployment_id" validate:"required"`
	StatusID     uint64 `json:"status_id" validate:"required"`
}

type UpdateServiceStatusRequest struct {
	StatusID uint64 `json:"status_id" validate:"required"`
}

type ServiceListItemResponse struct {
	ID         uint64                        `json:"id"`
	CreatedAt  time.Time                     `json:"created_at"`
	UpdatedAt  time.Time                     `json:"updated_at"`
	Name       string                        `json:"name"`
	Port       string                        `json:"port"`
	TargetPort string                        `json:"target_port"`
	Type       string                        `json:"type"`
	ClusterIP  string                        `json:"cluster_ip,omitempty"`
	ExternalIP string                        `json:"external_ip,omitempty"`
	IsActive   bool                          `json:"is_active"`
	Namespace  *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Deployment *DeploymentRelationResponse   `json:"deployment,omitempty"`
	Status     *ServerStatusRelationResponse `json:"status,omitempty"`
}

type ServiceListDnsResponse struct {
	ID         uint64                        `json:"id"`
	Name       string                        `json:"name"`
	Port       string                        `json:"port"`
	TargetPort string                        `json:"target_port"`
	Type       string                        `json:"type"`
	ClusterIP  string                        `json:"cluster_ip,omitempty"`
	ExternalIP string                        `json:"external_ip,omitempty"`
	IsActive   bool                          `json:"is_active"`
	Namespace  *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Deployment *DeploymentRelationResponse   `json:"deployment,omitempty"`
	Status     *ServerStatusRelationResponse `json:"status,omitempty"`
}

type ServiceDetailResponse struct {
	ID           uint64                         `json:"id"`
	CreatedAt    time.Time                      `json:"created_at"`
	UpdatedAt    time.Time                      `json:"updated_at"`
	Name         string                         `json:"name"`
	Port         string                         `json:"port"`
	TargetPort   string                         `json:"target_port"`
	Type         string                         `json:"type"`
	ClusterIP    string                         `json:"cluster_ip,omitempty"`
	ExternalIP   string                         `json:"external_ip,omitempty"`
	IsActive     bool                           `json:"is_active"`
	Namespace    *NamespaceRelationResponse     `json:"namespace,omitempty"`
	Deployment   *DeploymentRelationResponse    `json:"deployment,omitempty"`
	Status       *ServerStatusRelationResponse  `json:"status,omitempty"`
	IngressSpecs []*IngressSpecRelationResponse `json:"ingress_specs,omitempty"`
}

type ServiceRelationResponse struct {
	ID         uint64                        `json:"id"`
	Name       string                        `json:"name"`
	Port       string                        `json:"port"`
	TargetPort string                        `json:"target_port"`
	Type       string                        `json:"type"`
	ClusterIP  string                        `json:"cluster_ip,omitempty"`
	ExternalIP string                        `json:"external_ip,omitempty"`
	IsActive   bool                          `json:"is_active"`
	Status     *ServerStatusRelationResponse `json:"status,omitempty"`
}

type ServiceWithIngressResponse struct {
	ID           uint64                        `json:"id"`
	Name         string                        `json:"name"`
	Port         string                        `json:"port"`
	TargetPort   string                        `json:"target_port"`
	Type         string                        `json:"type"`
	ClusterIP    string                        `json:"cluster_ip,omitempty"`
	ExternalIP   string                        `json:"external_ip,omitempty"`
	IsActive     bool                          `json:"is_active"`
	Status       *ServerStatusRelationResponse `json:"status,omitempty"`
	IngressSpecs []IngressSpecRelationResponse `json:"ingress_specs,omitempty"`
}

type PublicServiceResponse struct {
	ID       uint64 `json:"id"`
	Name     string `json:"name"`
	IsActive bool   `json:"is_active"`
}

// Convert response functions

func ToServiceListItemDTO(s *domain.Service) *ServiceListItemResponse {
	resp := &ServiceListItemResponse{
		ID:         s.ID,
		CreatedAt:  s.CreatedAt,
		UpdatedAt:  s.UpdatedAt,
		Name:       s.Name,
		Port:       s.Port,
		TargetPort: s.TargetPort,
		Type:       s.Type,
		ClusterIP:  s.ClusterIP,
		ExternalIP: s.ExternalIP,
		IsActive:   s.IsActive,
		Status:     ToServerStatusRelationDTO(s.Status),
	}

	if s.Namespace != nil {
		resp.Namespace = ToNamespaceRelationDTO(s.Namespace)
	}
	if s.Deployment != nil {
		resp.Deployment = ToDeploymentRelationDTO(s.Deployment)
	}

	return resp
}

func ToServiceListDnsDTO(s *domain.Service) *ServiceListDnsResponse {
	if s.Name == "app" {
		s.Name = "@"
	}
	resp := &ServiceListDnsResponse{
		ID:         s.ID,
		Name:       s.Name,
		Port:       s.Port,
		TargetPort: s.TargetPort,
		Type:       s.Type,
		ClusterIP:  s.ClusterIP,
		ExternalIP: s.ExternalIP,
		IsActive:   s.IsActive,
		Status:     ToServerStatusRelationDTO(s.Status),
	}

	if s.Namespace != nil {
		resp.Namespace = ToNamespaceRelationDTO(s.Namespace)
	}
	if s.Deployment != nil {
		resp.Deployment = ToDeploymentRelationDTO(s.Deployment)
	}

	return resp
}

func ToServiceDetailDTO(s *domain.Service) *ServiceDetailResponse {
	resp := &ServiceDetailResponse{
		ID:         s.ID,
		CreatedAt:  s.CreatedAt,
		UpdatedAt:  s.UpdatedAt,
		Name:       s.Name,
		Port:       s.Port,
		TargetPort: s.TargetPort,
		Type:       s.Type,
		ClusterIP:  s.ClusterIP,
		ExternalIP: s.ExternalIP,
		IsActive:   s.IsActive,
		Status:     ToServerStatusRelationDTO(s.Status),
	}

	if s.Namespace != nil {
		resp.Namespace = ToNamespaceRelationDTO(s.Namespace)
	}

	if s.Deployment != nil {
		resp.Deployment = ToDeploymentRelationDTO(s.Deployment)
	}

	if s.IngressSpecs != nil && len(s.IngressSpecs) > 0 {
		for _, spec := range s.IngressSpecs {
			resp.IngressSpecs = append(resp.IngressSpecs, ToIngressSpecRelationDTO(spec))
		}
	}

	return resp
}

func ToServiceRelationDTO(s *domain.Service) *ServiceRelationResponse {
	return &ServiceRelationResponse{
		ID:         s.ID,
		Name:       s.Name,
		Port:       s.Port,
		TargetPort: s.TargetPort,
		Type:       s.Type,
		ClusterIP:  s.ClusterIP,
		ExternalIP: s.ExternalIP,
		IsActive:   s.IsActive,
		Status:     ToServerStatusRelationDTO(s.Status),
	}
}

func ToPublicServiceDTO(s *domain.Service) *PublicServiceResponse {
	return &PublicServiceResponse{
		ID:       s.ID,
		Name:     s.Name,
		IsActive: s.IsActive,
	}
}
