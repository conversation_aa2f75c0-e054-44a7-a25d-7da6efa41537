package services

import (
	"testing"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Simple mock for testing CheckDuplicateName
type mockOrderRepository struct {
	orders map[string]*domain.Order // Use name as key for simplicity
}

func newMockOrderRepository() *mockOrderRepository {
	return &mockOrderRepository{
		orders: make(map[string]*domain.Order),
	}
}

func (m *mockOrderRepository) Insert(order *domain.Order) error {
	m.orders[order.Name] = order
	return nil
}

func (m *mockOrderRepository) FindAll(filter *ports.OrderFilter) ([]*domain.Order, error) {
	return nil, nil
}

func (m *mockOrderRepository) FindByID(id uint64) (*domain.Order, error) {
	return nil, gorm.ErrRecordNotFound
}

func (m *mockOrderRepository) FindByName(name string) (*domain.Order, error) {
	if order, exists := m.orders[name]; exists {
		return order, nil
	}
	return nil, gorm.ErrRecordNotFound
}

func (m *mockOrderRepository) Update(order *domain.Order) error {
	return nil
}

func (m *mockOrderRepository) Delete(id uint64) error {
	return nil
}

func setupOrderService() (*OrderService, *mockOrderRepository) {
	orderRepo := newMockOrderRepository()

	service := &OrderService{
		orderRepo: orderRepo,
	}

	return service, orderRepo
}

func TestOrderService_CheckDuplicateName(t *testing.T) {
	service, orderRepo := setupOrderService()

	// Test 1: Check for non-existent name (should not be duplicate)
	isDuplicate, err := service.CheckDuplicateName("unique-name", nil)
	if err != nil {
		t.Fatalf("CheckDuplicateName failed: %v", err)
	}
	if isDuplicate {
		t.Error("Expected false for non-existent name, got true")
	}

	// Test 2: Add an order and check for duplicate
	order := &domain.Order{
		BaseModel:   domain.BaseModel{ID: 1},
		Name:        "existing-order",
		Code:        "TEST001",
		Description: "Test order",
		LineCode:    "LINE001",
		UserID:      1,
		TemplateID:  1,
	}
	orderRepo.Insert(order)

	isDuplicate, err = service.CheckDuplicateName("existing-order", nil)
	if err != nil {
		t.Fatalf("CheckDuplicateName failed: %v", err)
	}
	if !isDuplicate {
		t.Error("Expected true for existing name, got false")
	}

	// Test 3: Check with exclude_id (should not be duplicate when excluding the same order)
	isDuplicate, err = service.CheckDuplicateName("existing-order", &order.ID)
	if err != nil {
		t.Fatalf("CheckDuplicateName failed: %v", err)
	}
	if isDuplicate {
		t.Error("Expected false when excluding the same order, got true")
	}

	// Test 4: Check with different exclude_id (should still be duplicate)
	differentID := uint64(999)
	isDuplicate, err = service.CheckDuplicateName("existing-order", &differentID)
	if err != nil {
		t.Fatalf("CheckDuplicateName failed: %v", err)
	}
	if !isDuplicate {
		t.Error("Expected true when excluding different order, got false")
	}

	// Test 5: Check with empty name (should return error)
	_, err = service.CheckDuplicateName("", nil)
	if err == nil {
		t.Error("Expected error for empty name, got nil")
	}
}


