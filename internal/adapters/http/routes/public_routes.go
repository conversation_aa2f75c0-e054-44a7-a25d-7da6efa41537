package routes

import (
	"ops-api/internal/adapters/http/handlers"
	"ops-api/internal/adapters/http/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupPublicRoutes configures public API endpoints that require API key authentication
func SetupPublicRoutes(api fiber.Router, domainHandler *handlers.DomainHandler, serviceHandler *handlers.ServiceHandler, apiKey string) {
	// Create API key middleware
	apiKeyMiddleware := middleware.APIKeyMiddleware(apiKey)

	// Public routes group with API key authentication
	public := api.Group("/public", apiKeyMiddleware)

	// Domain endpoints
	public.Get("/domains", domainHandler.GetPublicDomains)
	public.Patch("/domains/:id/default", domainHandler.ChangePublicDomainDefault)

	// Service endpoints
	public.Get("/services/:line_code", serviceHandler.GetServiceByLineCode)
	public.Patch("/services/:id/active", serviceHandler.UpdateServiceActive)
}
